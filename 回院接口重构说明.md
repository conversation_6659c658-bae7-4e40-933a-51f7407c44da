# 回院接口重构说明

## 概述
重构了 `OutStopMedicineService.java` 中的回院接口，完善了用药医嘱预计到期日期的重新计算逻辑。

## 修改的文件
- `core-service/src/main/java/com/ruoyi/core/service/OutStopMedicineService.java`

## 主要功能
当患者回院时，系统会根据不同的给药方法重新计算用药医嘱的预计到期日期。

## 支持的给药方法

### 1. 简单给药方法
**适用于：** `qd`, `bid`, `tid`, `qid`, `qn`, `q4h`, `q6h`, `q8h`, `q12h`

**计算逻辑：** 原预计到期日期 + 外出天数

**示例：**
- 原预计到期日期：2025-08-10
- 外出天数：3天
- 新预计到期日期：2025-08-13

### 2. 隔天一次 (QOD)
**计算逻辑：**
1. 计算外出期间错过的用药次数：`(外出天数 + 1) / 2`（向上取整）
2. 每错过一次用药，延后2天
3. 新预计到期日期 = 原预计到期日期 + (错过次数 × 2)

**示例：**
- 原预计到期日期：2025-08-10
- 停药日期：2025-08-04
- 回院日期：2025-08-07
- 外出天数：3天
- 错过用药次数：(3+1)/2 = 2次
- 延后天数：2 × 2 = 4天
- 新预计到期日期：2025-08-14

### 3. 每周用药 (QW/BIW/TIW)
**适用于：**
- `qw`：一周一次
- `biw`：一周两次  
- `tiw`：一周三次

**计算逻辑：**
1. 解析星期设置（如"三，四"表示周三、周四用药）
2. 计算从停药后到原预计结束日期之间的剩余用药次数
3. 从回院日期开始，按照指定的星期几重新安排这些用药次数
4. 返回最后一次用药的日期

**示例（BIW - 一周两次）：**
- 用药日：周三、周四
- 原预计结束：2025-08-14（周四）
- 停药日期：2025-08-05（周二）
- 回院日期：2025-08-08（周五）
- 剩余用药次数：从8月6日到8月14日的周三、周四 = 3次（8月7日周三、8月14日周四、以及其他）
- 从8月8日开始重新安排：下一个周三是8月13日，然后8月14日周四，8月20日周三
- 新预计结束日期：根据剩余次数确定

## 核心方法说明

### `recalculateEstimatedExpirationDateForReturn()`
主要的重新计算方法，负责：
1. 查询用药医嘱的详细信息
2. 计算外出天数
3. 根据给药方法调用相应的计算逻辑
4. 更新数据库中的预计到期日期

### `calculateNewExpirationDate()`
根据不同给药方法分发到具体的计算逻辑

### `calculateQodNewExpirationDate()`
处理隔天一次的特殊计算逻辑

### `calculateWeeklyNewExpirationDate()`
处理每周用药的复杂计算逻辑

### 辅助方法
- `parseWeekDays()`：解析星期设置字符串
- `calculateRemainingWeeklyDoses()`：计算剩余用药次数
- `calculateNewWeeklyExpirationDate()`：重新安排每周用药日期
- `findNextMedicationDate()`：找到下一个用药日期
- `getWeekMonday()`：获取指定日期所在周的周一

## 数据库更新
更新 `medication_order` 表的以下字段：
- `estimated_expiration_date`：新的预计到期日期
- `update_time`：更新时间
- `update_by`：更新人

## 日志记录
- 成功更新时记录详细信息
- 异常情况记录警告和错误日志
- 包含关键参数便于调试

## 错误处理
- 对所有可能的异常进行捕获和处理
- 不影响主要的回院流程
- 提供详细的错误日志

## 测试建议
建议创建单元测试验证以下场景：
1. 各种给药方法的日期计算
2. 边界情况处理（如外出天数为0、负数等）
3. 数据库更新操作
4. 异常情况处理

## 注意事项
1. 该功能只对"用药医嘱"类型的医嘱生效
2. 需要确保 `medication_order` 表中有完整的给药方法和星期设置数据
3. 日期计算基于 HuTool 的 DateUtil 工具类
4. 星期几的映射：1=周一，7=周日
