package com.ruoyi.core.controller;

import com.ruoyi.core.service.MedicalHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/medical-history"})
public class MedicalHistoryController {
    private final MedicalHistoryService medicalHistoryService;

    public MedicalHistoryController(MedicalHistoryService medicalHistoryService) {
        this.medicalHistoryService = medicalHistoryService;
    }

    @GetMapping("/patient")
    public ResponseEntity<Object> getPatientList(
            @RequestParam(defaultValue = "") String sn,
            @RequestParam(defaultValue = "") String name,
            @RequestParam(defaultValue = "") String area,
            @RequestParam(defaultValue = "") String bed,
            @RequestParam String skip,
            @RequestParam String take
    ) {
        try {
            return ResponseEntity.ok().body(medicalHistoryService.getPatientList(sn, name, area, bed, skip, take));
        } catch (Exception e) {
            log.error("Error fetching patient list", e);
            return ResponseEntity.status(500).body("Internal Server Error");
        }
    }

    @PatchMapping("/{id}/{action}")
    public ResponseEntity<Object> patchMedicalHistory(
            @PathVariable String id,
            @PathVariable String action,
            @RequestBody Map<String, Object> data
    ) {
        try {
            this.medicalHistoryService.patchMedicalHistory(id, data, action);
            return ResponseEntity.ok().body(Map.of("msg", "操作成功"));
        } catch (Exception e) {
            log.error("Error updating medical history description for id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Object> updateMedicalHistory(
            @PathVariable String id,
            @RequestBody Map<String, Object> data
    ) {
        try {
            data.put("id", id);
            medicalHistoryService.updateMedicalHistory(data);
            return ResponseEntity.ok().body(Map.of("msg", "更新成功"));
        } catch (Exception e) {
            log.error("Error updating medical history with id: {}", id, e);
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getMedicalHistory(
            @PathVariable String id,
            @RequestParam Map<String, String> parameter
    ) {
        String option = parameter.getOrDefault("option", "");
        if ("".equals(option)) {
            try {
                Map<String, Object> medicalHistory = medicalHistoryService.getMedicalHistory(id);
                return ResponseEntity.ok().body(medicalHistory);
            } catch (Exception e) {
                log.error("Error fetching medical history with id: {}", id, e);
                return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
            }
        }
        if ("for-report".equals(option)) {
            try {
                Map<String, Object> medicalHistory = medicalHistoryService.getMedicalHistoryForReport(id);
                return ResponseEntity.ok().body(medicalHistory);
            } catch (Exception e) {
                log.error("Error fetching medical history for report with id: {}", id, e);
                return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
            }
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("msg", "参数错误"));
    }

    @GetMapping()
    public ResponseEntity<Object> getMedicalHistoryList(
            @RequestParam Map<String, String> parameter
    ) {
        String option = parameter.getOrDefault("option", "");
        if ("".equals(option)) {
            String sn = parameter.getOrDefault("sn", "");
            String name = parameter.getOrDefault("name", "");
            String skip = parameter.getOrDefault("skip", "0");
            String take = parameter.getOrDefault("take", "10");
            try {
                return ResponseEntity.ok().body(medicalHistoryService.getMedicalHistoryList(sn, name, skip, take));
            } catch (Exception e) {
                log.error("Error fetching medical history list", e);
                return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
            }
        }
        if ("by-relation_id".equals(option)) {
            String relationID = parameter.getOrDefault("relation_id", "");
            if (relationID.isBlank()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("msg", "relation_id不能为空"));
            }
            try {
                return ResponseEntity.ok().body(this.medicalHistoryService.getMedicalHistoryByRelationID(relationID));
            } catch (Exception e) {
                log.error("Error fetching medical history by relation_id: {}", relationID, e);
                return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
            }
        }
        if ("report".equals(option)) {
            String pageNum = parameter.getOrDefault("pageNum", "1");
            String pageSize = parameter.getOrDefault("pageSize", "10");
            String sn = parameter.getOrDefault("sn", "");
            String name = parameter.getOrDefault("name", "");
            String dateBegin = parameter.getOrDefault("dateBegin", "");
            String dateEnd = parameter.getOrDefault("dateEnd", "");
            try {
                String skip = Integer.parseInt(pageNum) > 1 ? String.valueOf((Integer.parseInt(pageNum) - 1) * Integer.parseInt(pageSize)) : "0";
                return ResponseEntity.ok().body(this.medicalHistoryService.getMedicalHistoryListForReport(sn, name, dateBegin, dateEnd, skip, pageSize));
            } catch (Exception e) {
                log.error("Error fetching medical history report", e);
                return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
            }
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("msg", "参数错误"));
    }

    @PostMapping()
    public ResponseEntity<Object> createMedicalHistory(
            @RequestBody Map<String, Object> data
    ) {
        try {
            String id = medicalHistoryService.createMedicalHistory(data);
            return ResponseEntity.status(HttpStatus.CREATED).body(Map.of("id", id));
        } catch (Exception e) {
            log.error("Error creating medical history", e);
            return ResponseEntity.status(500).body("Internal Server Error");
        }
    }
}
