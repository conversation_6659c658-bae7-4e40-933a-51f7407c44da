package com.ruoyi.core.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class GenericService {
    private final GenericMapper genericMapper;
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public GenericService(GenericMapper genericMapper, JdbcTemplate jdbcTemplate) {
        this.genericMapper = genericMapper;
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 根据json对象数组创建数据表 name/列名 type/类型 len/长度 pk/是否主键
     *
     * @param tableName 表名
     * @param columns   字段定义List，每个Map包含name/type/len
     */
    public void createTable(String tableName, List<Map<String, Object>> columns) {
        // 检查表是否已存在
        String checkSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
        Integer count = jdbcTemplate.query(checkSql, ps -> ps.setString(1, tableName), rs -> {
            if (rs.next()) {
                return rs.getInt(1);
            }
            return 0;
        });
        if (Objects.nonNull(count) && count > 0) {
            // 表已存在，获取已有字段
            String colSql = "SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_KEY FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = ?";
            List<Map<String, Object>> existCols = jdbcTemplate.queryForList(colSql, tableName);
            Set<String> existColNames = new HashSet<>();
            for (Map<String, Object> col : existCols) {
                existColNames.add(col.get("COLUMN_NAME").toString());
            }
            Set<String> newColNames = new HashSet<>();
            for (Map<String, Object> col : columns) {
                newColNames.add(col.get("name").toString());
            }
            // 新增字段
            for (Map<String, Object> col : columns) {
                String colName = col.get("name").toString();
                if (!existColNames.contains(colName)) {
                    StringBuilder alter = new StringBuilder("ALTER TABLE `" + tableName + "` ADD COLUMN `" + colName + "` " + col.get("type"));
                    if (col.get("len") != null) {
                        alter.append("(").append(col.get("len")).append(")");
                    }
                    jdbcTemplate.execute(alter.toString());
                }
            }
            // 删除字段
            for (Map<String, Object> existCol : existCols) {
                String existColName = existCol.get("COLUMN_NAME").toString();
                if (!newColNames.contains(existColName)) {
                    String alter = "ALTER TABLE `" + tableName + "` DROP COLUMN `" + existColName + "`";
                    jdbcTemplate.execute(alter);
                }
            }
        } else {
            StringBuilder sql = new StringBuilder("CREATE TABLE IF NOT EXISTS `" + tableName + "` (");
            List<String> pkColumns = new ArrayList<>();
            for (int i = 0; i < columns.size(); i++) {
                Map<String, Object> col = columns.get(i);
                sql.append("`").append(col.get("name")).append("` ")
                        .append(col.get("type"));
                if (col.get("len") != null) {
                    sql.append("(").append(col.get("len")).append(")");
                }
                if (Boolean.TRUE.equals(col.get("pk"))) {
                    pkColumns.add((String) col.get("name"));
                }
                if (i < columns.size() - 1) {
                    sql.append(", ");
                }
            }
            if (!pkColumns.isEmpty()) {
                sql.append(", PRIMARY KEY (");
                sql.append(String.join(", ", pkColumns));
                sql.append(")");
            }
            sql.append(")");
            jdbcTemplate.execute(sql.toString());
        }
    }

    /**
     * 根据leaf_id删除记录
     * 如果表中有d_status列，则将d_status设置为"1"，表示逻辑删除
     * 如果没有d_status列，则直接删除记录
     *
     * @param table  表名
     * @param leafID leaf_id值
     */
    public void removeByLeafID(String table, String leafID) {
        List<String[]> condition = new ArrayList<>();
        condition.add(new String[]{"equal", "leaf_id", leafID});
        String t = "`" + table + "`";
        List<Map<String, Object>> rows = genericMapper.getList(t, "*", condition, "limit 1");
        if (rows.isEmpty()) {
            throw new RuntimeException("没有找到leaf_id为 " + leafID + " 的记录");
        }
        Map<String, Object> row = rows.get(0);
        if (row.containsKey("d_status")) {
            // 如果表中有d_status列，设置d_status为"1"
            row.put("d_status", "1");
            this.genericMapper.update(t, row, Map.of("leaf_id", leafID));
        } else {
            // 如果没有d_status列，直接删除记录
            this.genericMapper.delete(table, condition);
        }
    }

    /**
     * 更新数据
     * 接收一个表名、leaf_id和一个包含数据的Map对象，更新指定记录
     * 数据格式：
     * { "column1": "value1", "column2": "value2" }
     *
     * @param table   表名
     * @param leaf_id leaf_id值
     * @param data    包含数据的Map对象
     */
    public void update(String table, String leaf_id, Map<String, Object> data) {
        // 查询表结构获取所有列名
        String sql = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = ? AND TABLE_SCHEMA = DATABASE()";
        List<String> columnNames = jdbcTemplate.queryForList(sql, String.class, table);

        if (columnNames.isEmpty()) {
            throw new RuntimeException("表 " + table + " 不存在或没有列");
        }

        table = "`" + table + "`";
        Set<String> validColumns = new HashSet<>(columnNames);

        // 验证data的key是否都是有效的列名
        for (String key : data.keySet()) {
            if (!validColumns.contains(key)) {
                throw new RuntimeException("列 " + key + " 在表 " + table + " 中不存在");
            }
        }

        // 如果表包含c_time列，设置c_time值为当前时间
        if (validColumns.contains("input_time")) {
            data.put("input_time", DateUtil.formatDateTime(new Date()));
        }
        if (validColumns.contains("upuser")) {
            try {
                data.put("upuser", SecurityUtils.getUserId().toString());
            } catch (Exception e) {
                data.put("upuser", "");
            }
        }
        if (validColumns.contains("x_upuser")) {
            try {
                data.put("x_upuser", SecurityUtils.getUsername());
            } catch (Exception e) {
                data.put("x_upuser", "");
            }
        }

        // 执行更新操作
        this.genericMapper.update(table, data, Map.of("leaf_id", leaf_id));
    }

    /**
     * 创建数据
     * 接收一个表名和一个包含数据的Map对象，创建一条新记录
     * 数据格式：
     * { "data": { "column1": "value1", "column2": "value2" }, "enc": { "column1": "encrypted_value1", "column2": "encrypted_value2" } }
     *
     * @param table 表名
     * @param data  包含数据的Map对象
     * @param enc   加密数据的Map对象
     */
    public String create(String table, Map<String, Object> data, Map<String, Object> enc) {
        String result = "";
        // 查询表结构获取所有列名
        String sql = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = ? AND TABLE_SCHEMA = DATABASE()";
        List<String> columnNames = this.jdbcTemplate.queryForList(sql, String.class, table);

        if (columnNames.isEmpty()) {
            throw new RuntimeException("表 " + table + " 不存在或没有列");
        }

        table = "`" + table + "`";
        Set<String> validColumns = new HashSet<>(columnNames);

        // 如果data中没有d_status且表中有d_status列，设置默认值为"0"
        if (!data.containsKey("d_status") && validColumns.contains("d_status")) {
            data.put("d_status", "0");
        }

        if (validColumns.contains("leaf_id") && !data.containsKey("leaf_id")) {
            result = IdUtil.getSnowflakeNextIdStr();
            data.put("leaf_id", result);
        }
        // 如果表包含creator列，设置creator值为"1123"
        if (validColumns.contains("creator")) {
            try {
                data.put("creator", SecurityUtils.getUserId().toString());
            } catch (Exception e) {
                data.put("creator", "");
            }
        }
        if (validColumns.contains("x_creator")) {
            try {
                data.put("x_creator", SecurityUtils.getUsername());
            } catch (Exception e) {
                data.put("x_creator", "");
            }
        }
        if (validColumns.contains("c_time")) {
            data.put("c_time", DateUtil.formatDateTime(new Date()));
        }

        // 验证data的key是否都是有效的列名
        for (String key : data.keySet()) {
            if (!validColumns.contains(key)) {
                throw new RuntimeException("列 " + key + " 在表 " + table + " 中不存在");
            }
        }

        // 构建INSERT语句
        if (!data.isEmpty()) {
            this.genericMapper.create(table, data);
        }
        return result;
    }

    /**
     * 获取数据列表
     * 根据表名、列名、条件和最后的限制获取数据列表
     * 如果列名为空，则默认为所有列（*）
     * 如果条件为空，则不添加任何条件
     * 如果最后的限制为空，则默认为"limit 10"
     *
     * @param table     表名
     * @param column    列名，逗号分隔的字符串，如果为空则默认为所有列（*）
     * @param condition 条件列表，每个条件为一个字符串数组，格式为["列名", "操作符", "值"]，如果为空则不添加任何条件
     *                  例如：[["name", "=", "John"], ["age", ">", "30"]]
     *                  如果条件列表为空，则不添加任何条件
     * @param last      最后的限制，默认为"limit 10"，如果为空则不添加限制
     *                  例如："limit 10" 或 "offset 5 limit 10"
     *                  如果为空则默认为"limit 10"
     * @return 数据列表，每个元素为一个Map，表示一行数据
     * @throws RuntimeException         如果表名为空或条件格式不正确
     * @throws IllegalArgumentException 如果表名或列名不合法
     * @throws NullPointerException     如果表名或列名为null
     */
    public List<Map<String, Object>> getList(String table, String column, List<String[]> condition, String last) {
        if (null != column && column.isEmpty()) {
            column = "*";
        }
        if (null != last && last.isEmpty()) {
            last = "limit 10";
        }
        List<Map<String, Object>> rows = genericMapper.getList(table, column, condition, last);
        return rows;
    }
}
