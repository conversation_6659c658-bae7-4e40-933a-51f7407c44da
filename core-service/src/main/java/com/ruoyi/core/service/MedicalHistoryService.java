package com.ruoyi.core.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * CREATE TABLE `yzlc`.`data_feat_medical_history`  (
 * `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
 * `data_state` json NULL,
 * `relation_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '病人 leaf_id',
 * `detail` json NULL COMMENT '生成人',
 * PRIMARY KEY (`id`) USING BTREE
 * ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '病史' ROW_FORMAT = Dynamic;
 */

@Service
public class MedicalHistoryService {
    public static final String TABLE_PATIENT = "data_ext_p000001321020140";

    //病史
    private final String TABLE_MEDICAL_HISTORY = "data_feat_medical_history";
    // 老人诊断
    private final String TABLE_DIAGNOSIS = "data_ext_laorzd";
    private final GenericMapper genericMapper;
    private final LogService logService;
    private final String MODULE = "病史";

    public MedicalHistoryService(GenericMapper genericMapper, LogService logService) {
        this.genericMapper = genericMapper;
        this.logService = logService;
    }

    /**
     * 更新病史数据
     *
     * @param id       病史ID
     * @param data     更新的数据
     * @param category 更新的类别，例如 "description", "physical_examine", "lab", "diagnosis", "plan"
     */
    @SuppressWarnings("unchecked")
    public void patchMedicalHistory(String id, Map<String, Object> data, String category) {
        List<Map<String, Object>> recordList = genericMapper.getList(
                TABLE_MEDICAL_HISTORY,
                "id,data_state,generated_at",
                List.<String[]>of(new String[]{"equal", "id", id}),
                "limit 1"
        );
        if (recordList == null || recordList.isEmpty()) {
            throw new RuntimeException("DataAccessControlService.update: record not found");
        }
        Map<String, Object> record = recordList.get(0);

        if ("generate".equals(category)) {
            if (Objects.isNull(record.get("generated_at"))) {
                data.put("generated_at", DateUtil.formatDateTime(new Date()));
            } else {
                return;
            }
        }

        String dataState = (String) record.get("data_state");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<Object, Object> dataStateMap;
        if (dataState != null && !dataState.isBlank()) {
            try {
                dataStateMap = objectMapper.readValue(dataState, Map.class);
                dataStateMap.put("updated_at", DateUtil.formatDateTime(new Date()));
                data.put("data_state", objectMapper.writeValueAsString(dataStateMap));
            } catch (JsonProcessingException e) {
                throw new RuntimeException("DataAccessControlService.update: data_state JSON parsing error", e);
            }
        }
        genericMapper.update(TABLE_MEDICAL_HISTORY, data, Map.of("id", id));
        data.put("id", id);
        if ("description".equals(category)) {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "更新病史描述", data, record);
        } else if ("physical_examine".equals(category)) {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "更新病史体格检查", data, record);
        } else if ("lab".equals(category)) {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "更新病史实验室与器械", data, record);
        } else if ("diagnosis".equals(category)) {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "更新病史初步诊断", data, record);
        } else if ("plan".equals(category)) {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "更新病史诊疗方案", data, record);
        } else if ("summary".equals(category)) {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "更新病史小结", data, record);
        } else if ("generate".equals(category)) {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "生成病史", data, record);
        } else {
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "更新病史-未定义操作", data, record);
        }
    }

    public Map<String, Object> getMedicalHistoryListForReport(String sn, String name, String dateBegin, String dateEnd, String skip, String take) {
        List<String[]> conditions = new ArrayList<>();
        if (sn != null && !sn.isBlank()) {
            conditions.add(new String[]{"equal", "old_num", sn});
        }
        if (name != null && !name.isBlank()) {
            conditions.add(new String[]{"equal", "old_name", name});
        }
        List<Map<String, Object>> patientList = genericMapper.getList(TABLE_PATIENT, "leaf_id,old_num,old_name,old_sex", conditions, "");
        List<String> patientIDs = patientList.stream()
                .map(map -> (String) map.get("leaf_id"))
                .distinct()
                .toList();
        if (patientIDs.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }

        conditions.clear();
        conditions.add(new String[]{"in", "relation_id", String.join(",", patientIDs)});
        if (Objects.nonNull(dateBegin) && !dateBegin.isBlank()) {
            conditions.add(new String[]{"greater-equal", "generated_at", dateBegin});
        }
        if (Objects.nonNull(dateEnd) && !dateEnd.isBlank()) {
            conditions.add(new String[]{"less-equal", "generated_at", dateEnd});
        }
        List<Map<String, Object>> totalList = genericMapper.getList(TABLE_MEDICAL_HISTORY, "count(*) as total", conditions, "");
        String total = totalList.isEmpty() ? "0" : String.valueOf(totalList.get(0).get("total"));
        if (Integer.parseInt(total) == 0) {
            return Map.of("rows", List.of(), "total", total);
        }
        List<Map<String, Object>> medicalHistoryList = genericMapper.getList(TABLE_MEDICAL_HISTORY, "id,relation_id,data_state,detail,generated_at", conditions, "limit " + skip + "," + take);
        Map<String, Map<String, Object>> patientMap = patientList.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("leaf_id"),
                        map -> map
                ));
        medicalHistoryList.forEach(map -> {
            String relationId = (String) map.get("relation_id");
            if (patientMap.containsKey(relationId)) {
                Map<String, Object> patient = patientMap.get(relationId);
                map.put("patient", patient);
            }
        });
        return Map.of("rows", medicalHistoryList, "total", total);
    }

    public Map<String, Object> getMedicalHistoryList(String sn, String name, String skip, String take) {
        List<String[]> conditions = new ArrayList<>();
        if (sn != null && !sn.isBlank()) {
            conditions.add(new String[]{"equal", "old_num", sn});
        }
        if (name != null && !name.isBlank()) {
            conditions.add(new String[]{"equal", "old_name", name});
        }
        List<Map<String, Object>> patientList = genericMapper.getList(TABLE_PATIENT, "leaf_id,old_num,old_name,old_sex", conditions, "");
        List<String> patientIds = patientList.stream()
                .map(map -> (String) map.get("leaf_id"))
                .distinct()
                .toList();

        if (patientIds.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }

        conditions.clear();
        conditions.add(new String[]{"in", "relation_id", String.join(",", patientIds)});

        List<Map<String, Object>> medicalHistoryList = genericMapper.getList(TABLE_MEDICAL_HISTORY, "*", conditions, "limit " + skip + "," + take);
        if (medicalHistoryList == null || medicalHistoryList.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        List<Map<String, Object>> totalList = genericMapper.getList(TABLE_MEDICAL_HISTORY, "count(*) as total", conditions, "");
        String total = totalList.isEmpty() ? "0" : String.valueOf(totalList.get(0).get("total"));
        Map<String, Map<String, Object>> patientMap = patientList.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("leaf_id"),
                        map -> map
                ));
        medicalHistoryList.forEach(map -> {
            String relationId = (String) map.get("relation_id");
            if (patientMap.containsKey(relationId)) {
                Map<String, Object> patient = patientMap.get(relationId);
                map.put("patient", patient);
            }
        });
        return Map.of("rows", medicalHistoryList, "total", total);
    }

    @SuppressWarnings("unchecked")
    public void updateMedicalHistory(Map<String, Object> data) throws JsonProcessingException {
        if (!data.containsKey("id")) {
            throw new RuntimeException("DataAccessControlService.update: id is required");
        }
        List<Map<String, Object>> record = genericMapper.getList(
                TABLE_MEDICAL_HISTORY,
                "*",
                List.<String[]>of(new String[]{"equal", "id", data.get("id").toString()}),
                ""
        );
        if (record == null || record.isEmpty()) {
            throw new RuntimeException("DataAccessControlService.update: record not found");
        }
        String dataState = (String) record.get(0).get("data_state");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<Object, Object> dataStateMap = new HashMap<>();
        if (dataState != null && !dataState.isBlank()) {
            dataStateMap = objectMapper.readValue(dataState, Map.class);
        }
        dataStateMap.put("updated_at", DateUtil.formatDateTime(new Date()));
        dataState = objectMapper.writeValueAsString(dataStateMap);
        data.put("data_state", dataState);
        Map<String, Object> condition = new HashMap<>();
        condition.put("id", data.get("id").toString());
        data.remove("id");
        genericMapper.update(TABLE_MEDICAL_HISTORY, data, condition);
    }

    public Map<String, Object> getMedicalHistoryForReport(String id) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "id", id});
        List<Map<String, Object>> medicalHistoryList = genericMapper.getList(TABLE_MEDICAL_HISTORY, "*", conditions, "");
        if (medicalHistoryList == null || medicalHistoryList.isEmpty()) {
            return Map.of();
        }
        Map<String, Object> medicalHistory = medicalHistoryList.get(0);
        String relationId = (String) medicalHistory.get("relation_id");
        if (relationId != null && !relationId.isBlank()) {
            conditions.clear();
            conditions.add(new String[]{"equal", "leaf_id", relationId});
            List<Map<String, Object>> patientList = genericMapper.getList(TABLE_PATIENT, "*", conditions, "");
            if (Objects.nonNull(patientList) && !patientList.isEmpty()) {
                Map<String, Object> patient = patientList.get(0);
                medicalHistory.put("patient", patient);
                String sn = (String) patient.get("old_num");
                conditions.clear();
                conditions.add(new String[]{"equal", "d_status", "0"});
                conditions.add(new String[]{"equal", "zhuyh", sn});
                List<Map<String, Object>> diagnosisList = genericMapper.getList(TABLE_DIAGNOSIS, "*", conditions, "");
                if (Objects.nonNull(diagnosisList) && !diagnosisList.isEmpty()) {
                    medicalHistory.put("diagnosis_list", diagnosisList);
                }
            }
        }
        return medicalHistory;
    }

    public List<Map<String, Object>> getMedicalHistoryByRelationID(String relationID) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "relation_id", relationID});
        List<Map<String, Object>> medicalHistoryList = genericMapper.getList(TABLE_MEDICAL_HISTORY, "id,relation_id", conditions, "");
        if (Objects.isNull(medicalHistoryList) || medicalHistoryList.isEmpty()) {
            return List.of();
        }
        return medicalHistoryList;
    }

    public Map<String, Object> getMedicalHistory(String id) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "id", id});
        List<Map<String, Object>> medicalHistoryList = genericMapper.getList(TABLE_MEDICAL_HISTORY, "*", conditions, "");
        if (medicalHistoryList == null || medicalHistoryList.isEmpty()) {
            return Map.of();
        }
        Map<String, Object> medicalHistory = medicalHistoryList.get(0);
        String relationId = (String) medicalHistory.get("relation_id");
        if (relationId != null && !relationId.isBlank()) {
            conditions.clear();
            conditions.add(new String[]{"equal", "leaf_id", relationId});
            List<Map<String, Object>> patientList = genericMapper.getList(TABLE_PATIENT, "*", conditions, "");
            if (patientList != null && !patientList.isEmpty()) {
                Map<String, Object> patient = patientList.get(0);
                medicalHistory.put("patient", patient);
            }
        }
        return medicalHistory;
    }

    public Map<String, Object> getPatientList(String sn, String name, String area, String bed, String skip, String take) {
        List<String[]> conditions = new ArrayList<>();
        if (sn != null && !sn.isBlank()) {
            conditions.add(new String[]{"equal", "old_num", sn});
        }
        if (name != null && !name.isBlank()) {
            conditions.add(new String[]{"equal", "old_name", name});
        }
        if (area != null && !area.isBlank()) {
            conditions.add(new String[]{"equal", "area_name", area});
        }
        if (bed != null && !bed.isBlank()) {
            conditions.add(new String[]{"equal", "bed_name", bed});
        }
        List<Map<String, Object>> patientList = genericMapper.getList(TABLE_PATIENT, "*", conditions, "limit " + skip + "," + take);
        if (patientList == null || patientList.isEmpty()) {
            return Map.of("total", "0", "rows", List.of());
        }
        List<Map<String, Object>> totalList = genericMapper.getList(TABLE_PATIENT, "count(*) as total", conditions, "");
        String total = totalList.isEmpty() ? "0" : String.valueOf(totalList.get(0).get("total"));
        return Map.of("rows", patientList, "total", total);
    }

    /**
     * 新增病史
     *
     * @param data 数据
     * @throws JsonProcessingException 如果数据状态转换为JSON时发生错误
     */
    public String createMedicalHistory(Map<String, Object> data) throws JsonProcessingException {
        data.put("id", IdUtil.getSnowflakeNextIdStr());

        Map<String, Object> dataState = new HashMap<>();
        dataState.put("created_at", DateUtil.formatDateTime(new Date()));
        dataState.put("status", "active");
        ObjectMapper objectMapper = new ObjectMapper();
        String dateStateString = objectMapper.writeValueAsString(dataState);
        data.put("data_state", dateStateString);

        data.put("description", "{}");
        data.put("physical_examine", "{}");

        genericMapper.create(TABLE_MEDICAL_HISTORY, data);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE, "新增病史", data, null);

        return (String) data.get("id");
    }
}
