package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.enums.MedicineOrderStatusEnum;
import com.ruoyi.core.mapper.GenericMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.ruoyi.core.service.LogService.MODULE_MEDICINE_ORDER;

@Slf4j
@Service
public class MedicineOrderService {

    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final GenericMapper genericMapper;
    private final LogService logService;

    public MedicineOrderService(NamedParameterJdbcTemplate namedParameterJdbcTemplate, GenericMapper genericMapper, LogService logService) {
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.genericMapper = genericMapper;
        this.logService = logService;
    }

    /**
     * @description 获取药品库存列表
     * <AUTHOR>
     */
    public Object inventory(Integer pageNum, Integer pageSize, String query) {
        String sql = """
                SELECT
                    IFNULL(y.leaf_id, '') AS leaf_id,
                    IFNULL(y.DrugsName, '') AS DrugsName,
                    IFNULL(y.zidym, '') AS zidym,
                    IFNULL(y.Standard, '') AS Standard,
                    IFNULL(y.PackingSize, '') AS PackingSize,
                    IFNULL(y.PackingUnit, '') AS PackingUnit,
                    IFNULL(y.menzdw, '') AS menzdw,
                    IFNULL(y.yaopdj, '') AS yaopdj,
                    IFNULL(y.chaifdj, '') AS chaifdj,
                    IFNULL(y.ways, '') AS ways,
                    IFNULL(y.geiyff, '') AS geiyff,
                    IFNULL(y.yongliang, '') AS yongliang,
                    IFNULL(k.InventoryNO, '') AS InventoryNO,
                    IFNULL(k.chufksl, '') AS chufksl,
                    IFNULL(k.chaifkcsl, '') AS chaifkcsl,
                    IFNULL(k.chaifsfksl, '') AS chaifsfksl,
                    IFNULL(y.yongldw, '') AS yongldw
                FROM
                    data_ext_kucgl AS k
                LEFT JOIN
                    data_ext_yaopxxgl AS y ON k.yaopin = y.leaf_id
                WHERE
                    1 = 1
                """;
        int offset = (pageNum - 1) * pageSize;
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        if (StrUtil.isNotBlank(query)) {
            params.put("query", "%" + query + "%");
            sql = sql + " and (y.DrugsName LIKE :query OR y.Pinyin LIKE :query)";
        }
        String countSql = "select count(*) from (" + sql + ") as count_table";
        sql = sql + """
                 order by k.c_time desc
                 LiMIT :offset , :pageSize
                """;
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
        Map<String, Object> map = createDefaultPageResult();
        if (CollectionUtil.isEmpty(list)) {
            return map;
        }
        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        map.replace("rows", list);
        map.replace("total", count);
        return map;
    }

    private Map<String, Object> createDefaultPageResult() {
        Map<String, Object> countMap = new HashMap<>();
        countMap.put("code", 200);
        countMap.put("rows", Collections.EMPTY_LIST);
        countMap.put("total", 0);
        return countMap;
    }

    /**
     * @description 新增用药医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add(Map<String, Object> params) {
        long id = IdUtil.getSnowflakeNextId();
        String username = SecurityUtils.getUsername();
        Map<String, Object> map = new HashMap<>(params);
        map.put("id", id);
        map.put("create_by", username);
        map.put("status", 0);
        genericMapper.create("medication_order", map);
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_MEDICINE_ORDER, "新增用药医嘱", map, null);
        return AjaxResult.success("添加成功", String.valueOf(id));
    }

    /**
     * @description 开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object prescribe(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要开方的医嘱");
        }

        // 生成统一的处方号和处方日期
        long prescriptionNumber = RandomUtil.randomLong(100000, 999999);
        String prescriptionDate = DateUtil.now();

        // 查询需要开方的医嘱
        String queryOrdersSql = """
                SELECT
                    id,
                    drug_stock_id,
                    prescription_quantity,
                    packaging_quantity,
                    packaging_unit
                FROM
                    medication_order
                WHERE
                    id IN (:ids)
                    AND is_deleted = 0
                    AND (status = 0 or status = 5)
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ids", ids);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到可开方的医嘱记录");
        }

        // 处理每个医嘱的库存扣减
        for (Map<String, Object> order : orders) {
            String drugStockId = order.get("drug_stock_id").toString();
            Integer prescriptionQuantity = Integer.parseInt(order.get("prescription_quantity").toString());

            Object packagingQuantityObj = order.get("packaging_quantity");
            Integer packagingQuantity = (packagingQuantityObj != null && !packagingQuantityObj.toString().isEmpty())
                    ? Integer.parseInt(packagingQuantityObj.toString()) : null;

            String packagingUnit = order.get("packaging_unit") != null ? order.get("packaging_unit").toString() : null;

            // 扣减库存
            AjaxResult stockCheckResult = checkAndUpdateStock(drugStockId, prescriptionQuantity, packagingQuantity, packagingUnit);
            if (stockCheckResult.isError()) {
                return AjaxResult.error("医嘱ID " + order.get("id") + " 库存不足");
            }
        }

        String username = SecurityUtils.getUsername();
        // 批量更新医嘱状态
        String updateOrderSql = """
                UPDATE medication_order
                SET status = 1,
                    prescription_number = :prescription_number,
                    prescription_date = :prescription_date,
                    prescriber = :prescriber,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("prescription_number", prescriptionNumber);
        updateParams.put("prescription_date", prescriptionDate);
        updateParams.put("prescriber", username);
        updateParams.put("update_by", username);
        updateParams.put("ids", ids);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 开方成功后，为每个医嘱插入doctor_order记录并计算预计到期日期
        for (Long id : ids) {
            insertDoctorOrderOnPrescribe(id, SecurityUtils.getUsername());
            // 计算并更新预计到期日期
            calculateAndUpdateEstimatedExpirationDate(id, prescriptionDate);
        }

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_MEDICINE_ORDER, "用药医嘱开方", ids, null);
        return AjaxResult.success("开方成功，处方号：" + prescriptionNumber);
    }

    /**
     * @description 取消开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object cancelPrescription(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要取消开方的医嘱");
        }

        // 查询需要取消开方的医嘱
        String queryOrdersSql = """
                SELECT
                    id,
                    drug_stock_id,
                    prescription_quantity,
                    packaging_quantity,
                    packaging_unit
                FROM
                    medication_order
                WHERE
                    id IN (:ids)
                    AND is_deleted = 0
                    AND status = 1
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ids", ids);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到已开方的医嘱记录");
        }

        // 处理每个医嘱的库存归还
        for (Map<String, Object> order : orders) {
            String drugStockId = order.get("drug_stock_id").toString();
            Integer prescriptionQuantity = Integer.parseInt(order.get("prescription_quantity").toString());

            Object packagingQuantityObj = order.get("packaging_quantity");
            Integer packagingQuantity = (packagingQuantityObj != null && !packagingQuantityObj.toString().isEmpty())
                    ? Integer.parseInt(packagingQuantityObj.toString()) : null;

            String packagingUnit = order.get("packaging_unit") != null ? order.get("packaging_unit").toString() : null;

            // 归还库存
            restoreStock(drugStockId, prescriptionQuantity, packagingQuantity, packagingUnit);
        }

        // 批量更新医嘱状态
        String updateOrderSql = """
                UPDATE medication_order
                SET status = 5,
                    prescription_number = NULL,
                    prescription_date = NULL,
                    prescriber = NULL,
                    estimated_expiration_date = NULL,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", SecurityUtils.getUsername());
        updateParams.put("ids", ids);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 删除doctor_order表中的记录
        for (Long id : ids) {
            deleteDoctorOrder(id);
        }

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_MEDICINE_ORDER, "用药医嘱取消开方", ids, null);
        return AjaxResult.success("取消开方成功");
    }

    /**
     * @description 已保存的用药医嘱列表
     * <AUTHOR>
     */
    public Object list(String patientId) {
        String sql = selectAllSql() + " where medical_record_id = :medical_record_id and is_deleted = 0";
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, Map.of("medical_record_id", patientId));
        if (CollectionUtil.isNotEmpty(list)) {
            // 为每个记录添加 status_label 字段
            for (Map<String, Object> record : list) {
                Object statusObj = record.get("status");
                if (statusObj != null) {
                    int status = Integer.parseInt(statusObj.toString());
                    record.put("status_label", MedicineOrderStatusEnum.getValueByKey(status));
                } else {
                    record.put("status_label", "未知状态");
                }
            }
        }
        return list;
    }

    /**
     * @description 用药医嘱详情
     * <AUTHOR>
     */
    public Object info(Long id, String patientId) {
        String sql = selectAllSql() + " where medical_record_id = :medical_record_id and id = :id and is_deleted = 0";
        Map<String, Object> map = namedParameterJdbcTemplate.queryForMap(sql, Map.of("id", id, "medical_record_id", patientId));

        // 添加 status_label 字段
        Object statusObj = map.get("status");
        if (statusObj != null) {
            int status = Integer.parseInt(statusObj.toString());
            map.put("status_label", MedicineOrderStatusEnum.getValueByKey(status));
        } else {
            map.put("status_label", "未知状态");
        }

        return AjaxResult.success(map);
    }

    private String selectAllSql() {
        return """
                SELECT
                CAST(id AS CHAR) AS id,
                medical_record_id,
                drug_stock_id,
                drug_name,
                drug_spec,
                drug_unit,
                status,
                part,
                administration_route,
                administration_method,
                prescription_quantity,
                packaging_quantity,
                packaging_unit,
                prescriber,
                week_day,
                morning_quantity,
                morning_time,
                noon_quantity,
                noon_time,
                evening_quantity,
                evening_time,
                night_quantity,
                night_time,
                prompt,
                group_number,
                unit_price,
                single_dose,
                single_dose_unit,
                order_date,
                CAST(attending_doctor_id AS CHAR) as attending_doctor_id,
                order_type,
                order_time,
                doctor_name,
                nurse_name,
                is_deleted,
                create_time,
                create_by,
                update_time,
                update_by,
                'exist' AS state
                FROM medication_order
                """;
    }

    /**
     * @return SQL字符串
     * @description 构建关联患者信息的查询SQL
     */
    private String selectAllWithPatientInfoSql() {
        return """
                SELECT
                    CAST(mo.id AS CHAR) AS id,
                    mo.medical_record_id,
                    mo.prescription_number,
                    mo.prescription_date,
                    mo.drug_stock_id,
                    mo.drug_name,
                    mo.drug_spec,
                    mo.drug_unit,
                    mo.status,
                    mo.part,
                    mo.administration_route,
                    mo.administration_method,
                    mo.prescription_quantity,
                    mo.packaging_quantity,
                    mo.packaging_unit,
                    mo.prescriber,
                    mo.week_day,
                    mo.morning_quantity,
                    mo.morning_time,
                    mo.noon_quantity,
                    mo.noon_time,
                    mo.evening_quantity,
                    mo.evening_time,
                    mo.night_quantity,
                    mo.night_time,
                    mo.prompt,
                    mo.group_number,
                    mo.unit_price,
                    mo.single_dose,
                    mo.single_dose_unit,
                    mo.order_date,
                    CAST(mo.attending_doctor_id AS CHAR) as attending_doctor_id,
                    mo.order_type,
                    mo.order_time,
                    mo.doctor_name,
                    mo.nurse_name,
                    mo.is_deleted,
                    mo.create_time,
                    mo.create_by,
                    mo.update_time,
                    mo.update_by,
                    IFNULL(mb.xingming, '') AS patient_name,
                    IFNULL(mb.chuangw_no, '') AS bed_number,
                    IFNULL(mb.zhuyuanh, '') AS hospitalization_id,
                    IFNULL(mb.quy, '') AS area,
                    'exist' as state
                FROM medication_order mo
                LEFT JOIN data_ext_menzbl mb ON mo.medical_record_id = mb.leaf_id
                """;
    }


    /**
     * @description 删除用药医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object delete(Long id) {
        genericMapper.update("medication_order", Map.of("is_deleted", 1), Map.of("id", id));
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_MEDICINE_ORDER, "删除用药医嘱", id, null);
        return AjaxResult.success("删除成功");
    }

    /**
     * @param drugStockId          药品ID
     * @param prescriptionQuantity 处方数量
     * @param packagingQuantity    包装量
     * @param packagingUnit        包装单位
     * @return 库存校验结果
     * @description 校验库存并更新库存数量
     */
    private AjaxResult checkAndUpdateStock(String drugStockId, Integer prescriptionQuantity,
                                           Integer packagingQuantity, String packagingUnit) {
        // 获取药品库存信息和药品基本信息
        String sql = """
                SELECT 
                    k.leaf_id, 
                    k.chufksl, 
                    k.chaifsfksl, 
                    y.PackingSize 
                FROM 
                    data_ext_kucgl AS k 
                LEFT JOIN 
                    data_ext_yaopxxgl AS y ON k.yaopin = y.leaf_id 
                WHERE 
                    k.yaopin = :drugStockId
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("drugStockId", drugStockId);

        Map<String, Object> drugInfo;
        try {
            drugInfo = namedParameterJdbcTemplate.queryForMap(sql, params);
        } catch (Exception e) {
            return AjaxResult.error("获取药品信息失败");
        }

        // 处理库存字段的数据类型
        int chufksl = Integer.parseInt(drugInfo.get("chufksl").toString());
        int chaifsfksl = Integer.parseInt(drugInfo.get("chaifsfksl").toString());
        int packingSize = Integer.parseInt(drugInfo.get("PackingSize").toString());

        // 计算库存扣减
        Map<String, Integer> stockDeduction = calculateStockDeduction(
                prescriptionQuantity, packagingQuantity, packagingUnit, chufksl, chaifsfksl, packingSize);

        if (stockDeduction.get("canDeduct") == 0) {
            return AjaxResult.error("处方数量大于处方库数量");
        }

        // 更新库存数量
        Map<String, Object> updateValues = new HashMap<>();
        updateValues.put("chufksl", chufksl - stockDeduction.get("chufkslDeduction"));
        updateValues.put("chaifsfksl", chaifsfksl - stockDeduction.get("chaifsfkslDeduction"));

        Map<String, Object> condition = new HashMap<>();
        condition.put("leaf_id", drugInfo.get("leaf_id"));

        genericMapper.update("data_ext_kucgl", updateValues, condition);

        return AjaxResult.success();
    }

    /**
     * @param prescriptionQuantity 处方数量
     * @param packagingQuantity    包装量
     * @param packagingUnit        包装单位
     * @param chufksl              处方库数量
     * @param chaifsfksl           拆分可发库数量
     * @param packingSize          包装规格
     * @return 库存扣减计算结果
     * @description 计算库存扣减
     */
    private Map<String, Integer> calculateStockDeduction(Integer prescriptionQuantity,
                                                         Integer packagingQuantity,
                                                         String packagingUnit,
                                                         int chufksl,
                                                         int chaifsfksl,
                                                         int packingSize) {
        Map<String, Integer> result = new HashMap<>();
        int chufkslDeduction = 0;
        int chaifsfkslDeduction = 0;
        int canDeduct = 1; // 1表示可以扣减，0表示不能扣减

        // 当包装量和包装单位为空时
        if (packagingQuantity == null || packagingUnit == null || packagingUnit.isEmpty()) {
            if (chufksl < prescriptionQuantity) {
                canDeduct = 0;
            } else {
                chufkslDeduction = prescriptionQuantity;
                chaifsfkslDeduction = prescriptionQuantity * packingSize;
            }
        }
        // 当包装量和包装单位不为空时
        else {
            if (chaifsfksl < prescriptionQuantity) {
                canDeduct = 0;
            } else {
                chaifsfkslDeduction = prescriptionQuantity;
                // 计算需要扣减的整包数量
                chufkslDeduction = (int) Math.ceil((double) prescriptionQuantity / packingSize);

                // 如果处方数量刚好是包装规格的整数倍，则不多扣
                if (prescriptionQuantity % packingSize == 0) {
                    chufkslDeduction = prescriptionQuantity / packingSize;
                }
            }
        }

        result.put("canDeduct", canDeduct);
        result.put("chufkslDeduction", chufkslDeduction);
        result.put("chaifsfkslDeduction", chaifsfkslDeduction);

        return result;
    }

    /**
     * @param drugStockId          药品ID
     * @param prescriptionQuantity 处方数量
     * @param packagingQuantity    包装量
     * @param packagingUnit        包装单位
     * @description 恢复药品库存
     */
    private void restoreStock(String drugStockId, Integer prescriptionQuantity,
                              Integer packagingQuantity, String packagingUnit) {
        // 获取药品库存信息和药品基本信息
        String sql = """
                SELECT 
                    k.leaf_id, 
                    k.chufksl, 
                    k.chaifsfksl, 
                    y.PackingSize 
                FROM 
                    data_ext_kucgl AS k 
                LEFT JOIN 
                    data_ext_yaopxxgl AS y ON k.yaopin = y.leaf_id 
                WHERE 
                    k.yaopin = :drugStockId
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("drugStockId", drugStockId);

        Map<String, Object> drugInfo;
        try {
            drugInfo = namedParameterJdbcTemplate.queryForMap(sql, params);
        } catch (Exception e) {
            return; // 如果查询失败，直接返回
        }

        // 处理库存字段的数据类型
        int chufksl = Integer.parseInt(drugInfo.get("chufksl").toString());
        int chaifsfksl = Integer.parseInt(drugInfo.get("chaifsfksl").toString());
        int packingSize = Integer.parseInt(drugInfo.get("PackingSize").toString());

        // 计算需要恢复的库存
        Map<String, Integer> stockRestore = calculateStockDeduction(
                prescriptionQuantity, packagingQuantity, packagingUnit, Integer.MAX_VALUE, Integer.MAX_VALUE, packingSize);

        // 更新库存数量（增加库存）
        Map<String, Object> updateValues = new HashMap<>();
        updateValues.put("chufksl", chufksl + stockRestore.get("chufkslDeduction"));
        updateValues.put("chaifsfksl", chaifsfksl + stockRestore.get("chaifsfkslDeduction"));

        Map<String, Object> condition = new HashMap<>();
        condition.put("leaf_id", drugInfo.get("leaf_id"));

        genericMapper.update("data_ext_kucgl", updateValues, condition);
    }

    /**
     * @description 修改用药医嘱
     * <AUTHOR>
     */
    public Object update(Long id, Map<String, Object> params) {
        // 检查医嘱是否存在且未开方
        String checkSql = "SELECT status FROM medication_order WHERE id = :id AND is_deleted = 0";
        Map<String, Object> checkParams = new HashMap<>();
        checkParams.put("id", id);

        Integer status;
        try {
            status = namedParameterJdbcTemplate.queryForObject(checkSql, checkParams, Integer.class);
        } catch (Exception e) {
            return AjaxResult.error("未找到医嘱记录");
        }

        // 只有未开方的医嘱才能修改
        if (status != null && status != 0 && status != 5) {
            return AjaxResult.error("已开方的医嘱不能修改");
        }

        // 更新医嘱
        String username = SecurityUtils.getUsername();
        Map<String, Object> updateData = new HashMap<>(params);
        updateData.put("update_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);

        genericMapper.update("medication_order", updateData, condition);
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_MEDICINE_ORDER, "修改用药医嘱", params, null);
        return AjaxResult.success("更新成功");
    }

    /**
     * @description 获取处方信息（处方号和处方日期）
     * <AUTHOR>
     */
    public Object getPrescriptionInfo(String patientId) {
        String sql = """
                SELECT
                    prescription_number,
                    prescription_date,
                    status
                FROM
                    medication_order
                WHERE
                    medical_record_id = :patientId
                    AND is_deleted = 0
                LIMIT 1
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("patientId", patientId);

        // 没有记录时返回空对象
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("prescription_number", null);
        emptyResult.put("prescription_date", null);
        emptyResult.put("flag", false);
        emptyResult.put("cancelFlag", false);
        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);
            if (result.get("status").toString().equals("0") || result.get("status").toString().equals("5")) {
                return AjaxResult.success(emptyResult);
            }
            String prescriptionDate = result.get("prescription_date").toString();
            DateTime dt = DateUtil.parse(prescriptionDate, "yyyy-MM-dd'T'HH:mm:ss");
            String formatted = DateUtil.format(dt, "yyyy-MM-dd HH:mm:ss");
            result.replace("prescription_date", formatted);
            // true为取消开方
            result.put("flag", true);
            if (result.get("status").toString().equals("1")) {
                result.put("cancelFlag", true);
            } else {
                result.put("cancelFlag", false);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.success(emptyResult);
        }
    }

    /**
     * @description 获取最近配药信息列表
     * <AUTHOR>
     */
    public Object getRecentMedicationHistory(String patientId, Integer pageNum, Integer pageSize, String query) {
        // 1. 根据patientId查询患者信息
        String patientInfoSql = """
                SELECT xingming, zhuyuanh
                FROM data_ext_menzbl
                WHERE leaf_id = :patientId
                """;

        Map<String, Object> patientParams = new HashMap<>();
        patientParams.put("patientId", patientId);

        Map<String, Object> patientInfo;
        try {
            patientInfo = namedParameterJdbcTemplate.queryForMap(patientInfoSql, patientParams);
        } catch (Exception e) {
            return createDefaultPageResult();
        }

        String xingming = patientInfo.get("xingming") != null ? patientInfo.get("xingming").toString() : "";
        String zhuyuanh = patientInfo.get("zhuyuanh") != null ? patientInfo.get("zhuyuanh").toString() : "";

        if (StrUtil.isBlank(xingming) || StrUtil.isBlank(zhuyuanh)) {
            return createDefaultPageResult();
        }

        // 2. 根据xingming和zhuyuanh查询最近5次门诊病历ID
        String recentRecordsSql = """
                SELECT leaf_id
                FROM data_ext_menzbl
                WHERE xingming = :xingming AND zhuyuanh = :zhuyuanh
                ORDER BY c_time DESC
                LIMIT 5
                """;

        Map<String, Object> recordParams = new HashMap<>();
        recordParams.put("xingming", xingming);
        recordParams.put("zhuyuanh", zhuyuanh);

        List<Map<String, Object>> recentRecords;
        try {
            recentRecords = namedParameterJdbcTemplate.queryForList(recentRecordsSql, recordParams);
        } catch (Exception e) {
            return createDefaultPageResult();
        }

        if (CollectionUtil.isEmpty(recentRecords)) {
            return createDefaultPageResult();
        }

        // 提取门诊病历ID列表
        List<String> medicalRecordIds = recentRecords.stream()
                .map(record -> record.get("leaf_id").toString())
                .toList();

        // 3. 根据门诊病历ID查询所有drug_stock_id
        String drugStockIdsSql = """
                SELECT DISTINCT drug_stock_id
                FROM medication_order
                WHERE medical_record_id IN (:medicalRecordIds)
                AND is_deleted = 0 and status != 5
                """;

        Map<String, Object> drugParams = new HashMap<>();
        drugParams.put("medicalRecordIds", medicalRecordIds);

        List<Map<String, Object>> drugStockIds;
        try {
            drugStockIds = namedParameterJdbcTemplate.queryForList(drugStockIdsSql, drugParams);
        } catch (Exception e) {
            return createDefaultPageResult();
        }

        if (CollectionUtil.isEmpty(drugStockIds)) {
            return createDefaultPageResult();
        }

        // 提取drug_stock_id列表
        List<String> drugIds = drugStockIds.stream()
                .map(drug -> drug.get("drug_stock_id").toString())
                .toList();

        // 4. 构建药品库存查询SQL（参考inventory方法）
        String sql = """
                SELECT
                    IFNULL(y.leaf_id, '') AS leaf_id,
                    IFNULL(y.DrugsName, '') AS DrugsName,
                    IFNULL(y.zidym, '') AS zidym,
                    IFNULL(y.Standard, '') AS Standard,
                    IFNULL(y.PackingSize, '') AS PackingSize,
                    IFNULL(y.PackingUnit, '') AS PackingUnit,
                    IFNULL(y.menzdw, '') AS menzdw,
                    IFNULL(y.yaopdj, '') AS yaopdj,
                    IFNULL(y.chaifdj, '') AS chaifdj,
                    IFNULL(y.ways, '') AS ways,
                    IFNULL(y.geiyff, '') AS geiyff,
                    IFNULL(y.yongliang, '') AS yongliang,
                    IFNULL(k.InventoryNO, '') AS InventoryNO,
                    IFNULL(k.chufksl, '') AS chufksl,
                    IFNULL(k.chaifkcsl, '') AS chaifkcsl,
                    IFNULL(k.chaifsfksl, '') AS chaifsfksl,
                    IFNULL(y.yongldw, '') AS yongldw
                FROM
                    data_ext_kucgl AS k
                LEFT JOIN
                    data_ext_yaopxxgl AS y ON k.yaopin = y.leaf_id
                WHERE
                    k.yaopin IN (:drugIds)
                """;

        // 5. 处理分页和查询条件
        int offset = (pageNum - 1) * pageSize;
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("drugIds", drugIds);

        if (StrUtil.isNotBlank(query)) {
            params.put("query", "%" + query + "%");
            sql = sql + " AND (y.DrugsName LIKE :query OR y.Pinyin LIKE :query)";
        }

        String countSql = "SELECT COUNT(*) FROM (" + sql + ") AS count_table";
        sql = sql + """
                 ORDER BY k.c_time DESC
                 LIMIT :offset, :pageSize
                """;

        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
        Map<String, Object> map = createDefaultPageResult();
        if (CollectionUtil.isEmpty(list)) {
            return map;
        }

        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        map.replace("rows", list);
        map.replace("total", count);
        return map;
    }

    /**
     * @description 获取用药医嘱分页列表
     * <AUTHOR>
     */
    public Object pageList(Integer pageNum, Integer pageSize, String quy, Integer status,
                           String orderType, String orderDate, String administrationRoute) {
        // 1. 如果有quy参数，先从data_ext_menzbl表中获取对应的leaf_id集合
        List<String> leafIds = null;
        if (StrUtil.isNotBlank(quy)) {
            String leafIdSql = """
                    SELECT leaf_id
                    FROM data_ext_menzbl
                    WHERE quy LIKE :quy
                    """;
            Map<String, Object> quyParams = new HashMap<>();
            quyParams.put("quy", "%" + quy + "%");

            try {
                List<Map<String, Object>> leafIdResults = namedParameterJdbcTemplate.queryForList(leafIdSql, quyParams);
                leafIds = leafIdResults.stream()
                        .map(row -> (String) row.get("leaf_id"))
                        .toList();

                // 如果没有找到匹配的leaf_id，直接返回空结果
                if (leafIds.isEmpty()) {
                    return createDefaultPageResult();
                }
            } catch (Exception e) {
                return createDefaultPageResult();
            }
        }

        // 2. 构建关联查询SQL，包含患者姓名和床位号
        String sql = selectAllWithPatientInfoSql() + " WHERE mo.is_deleted = 0 and mo.order_type != '继续开药'";

        int offset = (pageNum - 1) * pageSize;
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 3. 如果有leaf_id集合，添加IN条件
        if (CollectionUtil.isNotEmpty(leafIds)) {
            sql += " AND mo.medical_record_id IN (:leafIds)";
            params.put("leafIds", leafIds);
        }

        // 4. 如果有status参数，添加status条件
        if (status != null) {
            sql += " AND mo.status = :status";
            params.put("status", status);
        }

        // 5. 如果有orderType参数，添加精准匹配条件
        if (StrUtil.isNotBlank(orderType)) {
            sql += " AND mo.order_type = :orderType";
            params.put("orderType", orderType);
        }

        // 6. 如果有orderDate参数，添加精准匹配条件
        if (StrUtil.isNotBlank(orderDate)) {
            sql += " AND mo.order_date = :orderDate";
            params.put("orderDate", orderDate);
        }

        // 7. 如果有administrationRoute参数，添加精准匹配条件
        if (StrUtil.isNotBlank(administrationRoute)) {
            sql += " AND mo.administration_route = :administrationRoute";
            params.put("administrationRoute", administrationRoute);
        }

        // 8. 添加排序和分页
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") AS count_table";
        sql += " ORDER BY mo.create_time DESC LIMIT :offset, :pageSize";

        // 9. 执行查询
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
        Map<String, Object> result = createDefaultPageResult();

        if (CollectionUtil.isEmpty(list)) {
            return result;
        }

        // 10. 获取总数并返回结果
        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        result.replace("rows", list);
        result.replace("total", count);
        return result;
    }

    /**
     * @description 批量核对用药医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object batchVerify(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要核对的医嘱");
        }

        String username = SecurityUtils.getUsername();
        // 批量更新医嘱状态为已核对
        String updateOrderSql = """
                UPDATE medication_order
                SET status = 2,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                AND status = 1
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", username);
        updateParams.put("ids", ids);

        int updatedCount = namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_MEDICINE_ORDER, "批量核对用药医嘱", ids, null);
        return AjaxResult.success("核对成功，共核对 " + updatedCount + " 条医嘱");
    }

    /**
     * @description 批量生成用药医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object batchGenerate(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要生成的医嘱");
        }

        String username = SecurityUtils.getUsername();
        // 批量更新医嘱状态为已生成
        String updateOrderSql = """
                UPDATE medication_order
                SET status = 3,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                AND status = 2
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", username);
        updateParams.put("ids", ids);

        int updatedCount = namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_MEDICINE_ORDER, "批量生成用药医嘱", ids, null);
        return AjaxResult.success("生成成功，共生成 " + updatedCount + " 条医嘱");
    }

    /**
     * @description 获取上次使用情况
     * <AUTHOR>
     */
    public AjaxResult getLastUsageInfo(String patientId, String drugStockId) {
        // 1. 根据patientId查询患者信息
        String patientInfoSql = """
                SELECT xingming, zhuyuanh
                FROM data_ext_menzbl
                WHERE leaf_id = :patientId
                """;

        Map<String, Object> patientParams = new HashMap<>();
        patientParams.put("patientId", patientId);

        Map<String, Object> patientInfo;
        try {
            patientInfo = namedParameterJdbcTemplate.queryForMap(patientInfoSql, patientParams);
        } catch (Exception e) {
            return AjaxResult.success("");
        }

        String xingming = patientInfo.get("xingming") != null ? patientInfo.get("xingming").toString() : "";
        String zhuyuanh = patientInfo.get("zhuyuanh") != null ? patientInfo.get("zhuyuanh").toString() : "";

        if (StrUtil.isBlank(xingming) || StrUtil.isBlank(zhuyuanh)) {
            return AjaxResult.success("");
        }

        // 2. 根据xingming和zhuyuanh查询所有门诊病历ID
        String allRecordsSql = """
                SELECT leaf_id
                FROM data_ext_menzbl
                WHERE xingming = :xingming AND zhuyuanh = :zhuyuanh
                ORDER BY c_time DESC
                """;

        Map<String, Object> recordParams = new HashMap<>();
        recordParams.put("xingming", xingming);
        recordParams.put("zhuyuanh", zhuyuanh);

        List<Map<String, Object>> allRecords;
        try {
            allRecords = namedParameterJdbcTemplate.queryForList(allRecordsSql, recordParams);
        } catch (Exception e) {
            return AjaxResult.success("success", "");
        }

        if (CollectionUtil.isEmpty(allRecords)) {
            return AjaxResult.success("success", "");
        }

        // 提取所有门诊病历ID列表
        List<String> allMedicalRecordIds = allRecords.stream()
                .map(record -> record.get("leaf_id").toString())
                .toList();

        // 3. 根据门诊病历ID和drug_stock_id查询最近一条用药记录
        String lastUsageSql = """
                SELECT
                    drug_name,
                    drug_spec,
                    drug_unit,
                    prescription_quantity,
                    packaging_quantity,
                    packaging_unit
                FROM medication_order
                WHERE medical_record_id IN (:medicalRecordIds)
                AND drug_stock_id = :drugStockId
                AND is_deleted = 0 and status != 5
                ORDER BY create_time DESC
                LIMIT 1
                """;

        Map<String, Object> usageParams = new HashMap<>();
        usageParams.put("medicalRecordIds", allMedicalRecordIds);
        usageParams.put("drugStockId", drugStockId);

        Map<String, Object> lastUsage;
        try {
            lastUsage = namedParameterJdbcTemplate.queryForMap(lastUsageSql, usageParams);
        } catch (Exception e) {
            // 没有找到记录，返回空字符串
            return AjaxResult.success("success", "");
        }

        // 4. 构建返回字符串
        String drugName = lastUsage.get("drug_name") != null ? lastUsage.get("drug_name").toString() : "";
        String drugSpec = lastUsage.get("drug_spec") != null ? lastUsage.get("drug_spec").toString() : "";
        String drugUnit = lastUsage.get("drug_unit") != null ? lastUsage.get("drug_unit").toString() : "";
        Object prescriptionQuantityObj = lastUsage.get("prescription_quantity");
        Object packagingQuantityObj = lastUsage.get("packaging_quantity");
        String packagingUnit = lastUsage.get("packaging_unit") != null ? lastUsage.get("packaging_unit").toString() : "";

        String prescriptionQuantity = prescriptionQuantityObj != null ? prescriptionQuantityObj.toString() : "";
        String packagingQuantity = packagingQuantityObj != null ? packagingQuantityObj.toString() : "";

        String result;
        // 判断packaging_quantity和packaging_unit是否都有值
        if (StrUtil.isNotBlank(packagingQuantity) && StrUtil.isNotBlank(packagingUnit)) {
            // 都有值：drug_name + packaging_quantity + prescription_quantity + packaging_unit
            result = drugName + packagingQuantity + prescriptionQuantity + packagingUnit;
        } else {
            // 都没有值：drug_name + drug_spec + prescription_quantity + drug_unit
            result = drugName + drugSpec + prescriptionQuantity + drugUnit;
        }

        return AjaxResult.success("success", result);
    }

    /**
     * @param medicationOrderId 用药医嘱ID
     * @param username          用户名
     * @description 开方时插入医嘱表记录
     */
    private void insertDoctorOrderOnPrescribe(Long medicationOrderId, String username) {
        try {
            // 查询medication_order的完整数据
            String querySql = """
                    SELECT * FROM medication_order
                    WHERE id = :id AND is_deleted = 0 and order_type != '继续开药'
                    """;
            Map<String, Object> medicationData;
            try {
                medicationData = namedParameterJdbcTemplate.queryForMap(
                        querySql, Map.of("id", medicationOrderId));
            } catch (Exception e) {
                return;
            }

            // 获取患者信息
            String medicalRecordId = medicationData.get("medical_record_id").toString();
            Map<String, Object> patientInfo = getPatientInfo(medicalRecordId);

            // 生成医嘱详细内容
            String content = generateOrderContent(medicationData);

            // 构建doctor_order数据
            Map<String, Object> doctorOrderData = new HashMap<>();
            doctorOrderData.put("id", IdUtil.getSnowflakeNextId());
            doctorOrderData.put("medication_order_id", medicationOrderId);
            doctorOrderData.put("area", patientInfo.get("quy"));
            doctorOrderData.put("bed_number", patientInfo.get("chuangw_no"));
            doctorOrderData.put("hospitalization_id", patientInfo.get("zhuyuanh"));
            doctorOrderData.put("patient_name", patientInfo.get("xingming"));
            doctorOrderData.put("order_time", DateUtil.now());
            doctorOrderData.put("doctor_name", username);
            doctorOrderData.put("order_name", "用药医嘱");
            doctorOrderData.put("order_type", medicationData.get("order_type").toString());
            doctorOrderData.put("content", content);
            doctorOrderData.put("created_by", username);
            doctorOrderData.put("is_deleted", 0);
            doctorOrderData.put("status", 0);
            if (medicationData.get("order_type").toString().equals("长期")) {
                doctorOrderData.put("is_stopped", 0);
            }
            genericMapper.create("doctor_order", doctorOrderData);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("开方时插入医嘱表失败: " + e.getMessage());
        }
    }


    /**
     * @param medicationOrderId 用药医嘱ID
     * @description 删除医嘱表记录
     */
    private void deleteDoctorOrder(Long medicationOrderId) {
        try {
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("is_deleted", 1);

            Map<String, Object> condition = new HashMap<>();
            condition.put("medication_order_id", medicationOrderId);

            genericMapper.update("doctor_order", updateData, condition);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("删除医嘱表记录失败: " + e.getMessage());
        }
    }

    /**
     * @param medicalRecordId 门诊病历ID
     * @return 患者信息
     * @description 获取患者信息
     */
    private Map<String, Object> getPatientInfo(String medicalRecordId) {
        String sql = """
                SELECT quy, chuangw_no, zhuyuanh, xingming
                FROM data_ext_menzbl
                WHERE leaf_id = :medicalRecordId
                """;
        try {
            return namedParameterJdbcTemplate.queryForMap(sql, Map.of("medicalRecordId", medicalRecordId));
        } catch (Exception e) {
            // 返回默认值
            Map<String, Object> defaultInfo = new HashMap<>();
            defaultInfo.put("quy", "");
            defaultInfo.put("chuangw_no", "");
            defaultInfo.put("zhuyuanh", "");
            defaultInfo.put("xingming", "");
            return defaultInfo;
        }
    }

    /**
     * @param medicationData 用药医嘱数据
     * @return 医嘱详细内容
     * @description 生成医嘱详细内容
     */
    private String generateOrderContent(Map<String, Object> medicationData) {
        String drugName = getStringValue(medicationData, "drug_name");
        String drugSpec = getStringValue(medicationData, "drug_spec");
        String drugUnit = getStringValue(medicationData, "drug_unit");
        String singleDose = getStringValue(medicationData, "single_dose");
        String administrationMethod = getStringValue(medicationData, "administration_method");
        String administrationRoute = getStringValue(medicationData, "administration_route");
        String prompt = getStringValue(medicationData, "prompt");
        String orderType = getStringValue(medicationData, "order_type");

        Object prescriptionQuantityObj = medicationData.get("prescription_quantity");
        String prescriptionQuantity = prescriptionQuantityObj != null ? prescriptionQuantityObj.toString() : "";

        // 将"口服"替换为"po"
        if ("口服".equals(administrationRoute)) {
            administrationRoute = "po";
        }

        StringBuilder content = new StringBuilder();

        if ("长期".equals(orderType)) {
            // 长期医嘱：药品名称+次用量+给药方法+给药途径+提示
            content.append(drugName);
            if (StrUtil.isNotBlank(singleDose)) {
                content.append(singleDose);
            }
            if (StrUtil.isNotBlank(administrationMethod)) {
                content.append(administrationMethod);
            }
            if (StrUtil.isNotBlank(administrationRoute)) {
                content.append(administrationRoute);
            }
            if (StrUtil.isNotBlank(prompt)) {
                content.append(prompt);
            }
        } else if ("临时".equals(orderType)) {
            // 临时医嘱：药品名称+规格+数量+sig：+次用量+给药方法+给药途径+提示
            content.append(drugName);
            if (StrUtil.isNotBlank(drugSpec)) {
                content.append(drugSpec);
            }
            if (StrUtil.isNotBlank(prescriptionQuantity)) {
                content.append(prescriptionQuantity);
            }
            content.append("sig：");
            if (StrUtil.isNotBlank(singleDose)) {
                content.append(singleDose);
            }
            if (StrUtil.isNotBlank(administrationMethod)) {
                content.append(administrationMethod);
            }
            if (StrUtil.isNotBlank(administrationRoute)) {
                content.append(administrationRoute);
            }
            if (StrUtil.isNotBlank(prompt)) {
                content.append(prompt);
            }
        } else {
            // 继续开药：暂时不需要内容，返回空字符串
            return "";
        }

        return content.toString();
    }

    /**
     * @param map 数据Map
     * @param key 键
     * @return 字符串值，如果为null则返回空字符串
     * @description 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * @description 获取所有护理部的人员
     * <AUTHOR>
     */
    public Object getNurse() {
        Long deptId;
        try {
            String deptSql = "select dept_id from sys_dept where dept_name = :dept_name";
            deptId = namedParameterJdbcTemplate.queryForObject(deptSql,
                    Map.of("dept_name", "护理部"), Long.class);
        } catch (Exception e) {
            return Collections.EMPTY_LIST;
        }
        String sql = "select CAST(user_id AS CHAR) as user_id,user_name from sys_user where dept_id = :dept_id";
        List<Map<String, Object>> result = namedParameterJdbcTemplate.queryForList(sql, Map.of("dept_id", deptId));
        if (CollectionUtil.isEmpty(result)) {
            return List.of();
        }
        return result;
    }

    /**
     * 计算并更新预计到期日期
     *
     * @param medicationOrderId 用药医嘱ID
     * @param prescriptionDate  处方日期
     */
    private void calculateAndUpdateEstimatedExpirationDate(Long medicationOrderId, String prescriptionDate) {
        try {
            // 查询医嘱信息
            String querySql = """
                    SELECT
                        single_dose,
                        single_dose_unit,
                        administration_method,
                        prescription_quantity,
                        packaging_quantity,
                        packaging_unit,
                        morning_quantity,
                        noon_quantity,
                        evening_quantity,
                        night_quantity,
                        prompt,
                        drug_stock_id,
                        order_type,
                        week_day
                    FROM medication_order
                    WHERE id = :id AND is_deleted = 0
                    """;

            Map<String, Object> params = new HashMap<>();
            params.put("id", medicationOrderId);

            Map<String, Object> orderInfo;
            try {
                orderInfo = namedParameterJdbcTemplate.queryForMap(querySql, params);
            } catch (Exception e) {
                return; // 查询失败，直接返回
            }

            String singleDose = (String) orderInfo.get("single_dose");
            String singleDoseUnit = (String) orderInfo.get("single_dose_unit");
            String administrationMethod = (String) orderInfo.get("administration_method");
            String orderType = (String) orderInfo.get("order_type");

            // 只有医嘱类型是"长期"才执行计算逻辑
            if (orderType == null || !"长期".equals(orderType.trim())) {
                return; // 非长期医嘱，不计算预计到期日期
            }

            // 检查次用量和给药方法是否为空
            if (singleDose == null || singleDose.trim().isEmpty() ||
                    administrationMethod == null || administrationMethod.trim().isEmpty()) {
                return; // 必要字段为空，不计算
            }

            // 计算药品总量
            Double totalDrugQuantity = calculateTotalDrugQuantity(orderInfo);
            if (totalDrugQuantity == null || totalDrugQuantity <= 0) {
                return; // 无法计算药品总量
            }

            // 解析次用量
            Double singleDoseAmount = parseSingleDoseAmount(singleDose, singleDoseUnit);
            if (singleDoseAmount == null || singleDoseAmount <= 0) {
                return; // 无法解析次用量
            }

            // 计算预计到期日期
            Date estimatedExpirationDate = calculateEstimatedExpirationDateNew(
                    prescriptionDate, totalDrugQuantity, singleDoseAmount, administrationMethod,
                    (String) orderInfo.get("prompt"), (String) orderInfo.get("week_day"), orderInfo);

            if (estimatedExpirationDate != null) {
                // 更新预计到期日期
                String updateSql = """
                        UPDATE medication_order
                        SET estimated_expiration_date = :estimated_expiration_date,
                            update_time = NOW(),
                            update_by = :update_by
                        WHERE id = :id
                        """;

                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("estimated_expiration_date", estimatedExpirationDate);
                updateParams.put("update_by", SecurityUtils.getUsername());
                updateParams.put("id", medicationOrderId);

                namedParameterJdbcTemplate.update(updateSql, updateParams);
            }

        } catch (Exception e) {
            // 计算失败，记录日志但不影响开方流程
            e.printStackTrace();
        }
    }


    /**
     * 计算每日用量
     *
     * @param singleDose           次用量
     * @param singleDoseUnit       次用量单位
     * @param administrationMethod 给药方法
     * @param orderInfo            医嘱信息
     * @return 每日用量
     */
    private Double calculateDailyDosage(String singleDose, String singleDoseUnit,
                                        String administrationMethod, Map<String, Object> orderInfo) {
        try {
            // 解析次用量中的数字
            Double singleDoseAmount = parseSingleDoseAmount(singleDose, singleDoseUnit);
            if (singleDoseAmount == null || singleDoseAmount <= 0) {
                return null;
            }

            // 解析给药方法的倍数
            Double methodMultiplier = parseAdministrationMethodMultiplier(administrationMethod, orderInfo);
            if (methodMultiplier == null || methodMultiplier <= 0) {
                return null;
            }

            return singleDoseAmount * methodMultiplier;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析次用量中的数字
     *
     * @param singleDose     次用量
     * @param singleDoseUnit 次用量单位
     * @return 次用量数值
     */
    private Double parseSingleDoseAmount(String singleDose, String singleDoseUnit) {
        try {
            // 检查次用量是否为纯数字
            if (singleDose.matches("^\\d+(\\.\\d+)?$")) {
                return Double.parseDouble(singleDose);
            }

            // 如果不是纯数字，根据次用量单位匹配字符串中的数字
            if (singleDoseUnit != null && !singleDoseUnit.trim().isEmpty()) {
                // 查找单位前的所有数字并求和
                String pattern = "\\d+(\\.\\d+)?(?=" + singleDoseUnit + "|$)";
                java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
                java.util.regex.Matcher m = p.matcher(singleDose);

                double total = 0;
                while (m.find()) {
                    total += Double.parseDouble(m.group());
                }

                if (total > 0) {
                    return total;
                }
            }

            // 如果单位为空或匹配失败，尝试提取所有数字求和
            java.util.regex.Pattern p = java.util.regex.Pattern.compile("\\d+(\\.\\d+)?");
            java.util.regex.Matcher m = p.matcher(singleDose);

            double total = 0;
            while (m.find()) {
                total += Double.parseDouble(m.group());
            }

            return total > 0 ? total : null;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析给药方法的倍数
     *
     * @param administrationMethod 给药方法
     * @param orderInfo            医嘱信息
     * @return 给药方法倍数
     */
    private Double parseAdministrationMethodMultiplier(String administrationMethod, Map<String, Object> orderInfo) {
        try {
            String method = administrationMethod.toLowerCase().trim();

            return switch (method) {
                case "qd" -> 1.0;
                case "bid" -> 2.0;
                case "tid" -> 3.0;
                case "qid" -> 4.0;
                case "qn" -> 1.0;
                case "q4h", "q6h", "q8h", "q12h" ->
                    // 根据早、中、晚、夜数量字段计算一天几次
                        calculateDailyFrequencyFromQuantities(orderInfo);
                case "qod" ->
                    // 隔天1次，特殊处理
                        0.5; // 每天0.5次
                case "qw" ->
                    // 一周一次
                        1.0 / 7.0; // 每天1/7次
                case "biw" ->
                    // 一周两次
                        2.0 / 7.0; // 每天2/7次
                case "tiw" ->
                    // 一周三次
                        3.0 / 7.0; // 每天3/7次
                default -> null;
            };

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据早中晚夜数量计算每日频次
     *
     * @param orderInfo 医嘱信息
     * @return 每日频次
     */
    private Double calculateDailyFrequencyFromQuantities(Map<String, Object> orderInfo) {
        try {
            int count = 0;

            Object morningQuantity = orderInfo.get("morning_quantity");
            if (morningQuantity != null && Double.parseDouble(morningQuantity.toString()) > 0) {
                count++;
            }

            Object noonQuantity = orderInfo.get("noon_quantity");
            if (noonQuantity != null && Double.parseDouble(noonQuantity.toString()) > 0) {
                count++;
            }

            Object eveningQuantity = orderInfo.get("evening_quantity");
            if (eveningQuantity != null && Double.parseDouble(eveningQuantity.toString()) > 0) {
                count++;
            }

            Object nightQuantity = orderInfo.get("night_quantity");
            if (nightQuantity != null && Double.parseDouble(nightQuantity.toString()) > 0) {
                count++;
            }

            return count > 0 ? (double) count : null;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 计算药品总量
     *
     * @param orderInfo 医嘱信息
     * @return 药品总量
     */
    private Double calculateTotalDrugQuantity(Map<String, Object> orderInfo) {
        try {
            Object prescriptionQuantityObj = orderInfo.get("prescription_quantity");
            Object packagingQuantityObj = orderInfo.get("packaging_quantity");
            Object packagingUnitObj = orderInfo.get("packaging_unit");

            if (prescriptionQuantityObj == null) {
                return null;
            }

            int prescriptionQuantity = Integer.parseInt(prescriptionQuantityObj.toString());

            // 如果包装量和包装单位不为空，则药品总量就是处方数量的值
            if (packagingQuantityObj != null && packagingUnitObj != null &&
                    !packagingUnitObj.toString().trim().isEmpty()) {
                return (double) prescriptionQuantity;
            }

            // 如果包装量和包装单位为空，则从药品信息表中查询包装量
            String drugStockId = (String) orderInfo.get("drug_stock_id");
            if (drugStockId == null || drugStockId.trim().isEmpty()) {
                return null;
            }

            String sql = """
                    SELECT PackingSize
                    FROM data_ext_yaopxxgl
                    WHERE leaf_id = :drugStockId
                    """;

            Map<String, Object> params = new HashMap<>();
            params.put("drugStockId", drugStockId);

            try {
                String packingSizeStr = namedParameterJdbcTemplate.queryForObject(sql, params, String.class);
                if (packingSizeStr != null && !packingSizeStr.trim().isEmpty()) {
                    int packingSize = Integer.parseInt(packingSizeStr);
                    return (double) (packingSize * prescriptionQuantity);
                }
            } catch (Exception e) {
                // 查询失败，返回null
            }

            return null;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 计算预计到期日期（新逻辑）
     *
     * @param prescriptionDate     处方日期
     * @param totalDrugQuantity    药品总量
     * @param singleDoseAmount     次用量
     * @param administrationMethod 给药方法
     * @param prompt               提示
     * @param weekDay              星期设置
     * @param orderInfo            医嘱信息
     * @return 预计到期日期
     */
    private Date calculateEstimatedExpirationDateNew(String prescriptionDate, Double totalDrugQuantity,
                                                     Double singleDoseAmount, String administrationMethod,
                                                     String prompt, String weekDay, Map<String, Object> orderInfo) {
        try {
            // 解析处方日期
            Date startDate = DateUtil.parseDate(prescriptionDate);

            // 检查提示中是否包含"明起"
            boolean startFromTomorrow = prompt != null && prompt.contains("明起");

            // 判断次用量是否为纯数字
            String singleDose = (String) orderInfo.get("single_dose");
            boolean isPureNumber = singleDose != null && singleDose.matches("^\\d+(\\.\\d+)?$");

            // 计算可以用药的总次数
            int totalDoses = (int) Math.floor(totalDrugQuantity / singleDoseAmount);
            if (totalDoses <= 0) {
                return null;
            }

            String method = administrationMethod.toLowerCase().trim();
            Date expirationDate;

            switch (method) {
                case "qod": // 隔天1次
                    // 每2天用药1次，总共用药totalDoses次
                    // 最后一次用药的日期 = 开始日期 + (totalDoses - 1) * 2天
                    int daysForQod = (totalDoses - 1) * 2;
                    if (startFromTomorrow) {
                        expirationDate = DateUtil.offsetDay(startDate, 1 + daysForQod);
                    } else {
                        expirationDate = DateUtil.offsetDay(startDate, daysForQod);
                    }
                    break;

                case "qw": // 一周一次
                    expirationDate = calculateWeeklyMedicationDate(startDate, totalDoses, weekDay, 1, startFromTomorrow);
                    break;

                case "biw": // 一周两次
                    expirationDate = calculateWeeklyMedicationDate(startDate, totalDoses, weekDay, 2, startFromTomorrow);
                    break;

                case "tiw": // 一周三次
                    expirationDate = calculateWeeklyMedicationDate(startDate, totalDoses, weekDay, 3, startFromTomorrow);
                    break;

                default:
                    // 其他给药方法：计算每日用量和可用天数
                    Double methodMultiplier = parseAdministrationMethodMultiplier(administrationMethod, orderInfo);
                    double dailyDosage;
                    // 根据次用量类型采用不同的计算规则
                    if (methodMultiplier != null && methodMultiplier > 0) {
                        if (isPureNumber) {
                            // 纯数字：每日用量 = 次用量 × 给药方法
                            dailyDosage = singleDoseAmount * methodMultiplier;
                        } else {
                            // 不是纯数字：每日用量 = 正则匹配出的数字的总和
                            dailyDosage = singleDoseAmount;
                        }
                    } else {
                        dailyDosage = singleDoseAmount;
                    }
                    int availableDays = (int) Math.floor(totalDrugQuantity / dailyDosage);

                    if (startFromTomorrow) {
                        expirationDate = DateUtil.offsetDay(startDate, availableDays);
                    } else {
                        expirationDate = DateUtil.offsetDay(startDate, availableDays - 1);
                    }
                    break;
            }

            return expirationDate;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 计算每周用药的预计到期日期
     *
     * @param startDate               开始日期
     * @param totalDoses              总用药次数
     * @param weekDay                 星期设置（如"一，二"）
     * @param expectedWeeklyFrequency 预期每周频次（1=QW, 2=BIW, 3=TIW）
     * @param startFromTomorrow       是否从明天开始
     * @return 预计到期日期
     */
    private Date calculateWeeklyMedicationDate(Date startDate, int totalDoses, String weekDay,
                                               int expectedWeeklyFrequency, boolean startFromTomorrow) {
        try {
            if (weekDay == null || weekDay.trim().isEmpty()) {
                // 如果没有指定星期，按照固定间隔计算
                int intervalDays = 7 / expectedWeeklyFrequency;
                int totalDays = (totalDoses - 1) * intervalDays;
                return startFromTomorrow ?
                        DateUtil.offsetDay(startDate, 1 + totalDays) :
                        DateUtil.offsetDay(startDate, totalDays);
            }

            // 解析星期设置
            List<Integer> weekDays = parseWeekDays(weekDay, expectedWeeklyFrequency);
            if (weekDays.isEmpty()) {
                return null;
            }

            // 计算实际的用药日期
            Date currentDate = startFromTomorrow ? DateUtil.offsetDay(startDate, 1) : startDate;
            List<Date> medicationDates = new ArrayList<>();

            // 获取当前日期是星期几（1=周一，7=周日）
            int currentWeekDay = DateUtil.dayOfWeek(currentDate);
            if (currentWeekDay == 1) currentWeekDay = 7; // 周日从1转换为7
            else currentWeekDay = currentWeekDay - 1; // 其他从2-7转换为1-6

            // 找到第一个用药日期（从今天开始找最近的选择星期几）
            Date firstMedicationDate = null;

            // 检查今天是否是选择的星期几
            if (weekDays.contains(currentWeekDay)) {
                firstMedicationDate = currentDate;
            } else {
                // 找到本周内下一个选择的星期几
                Date foundInThisWeek = null;
                for (int targetWeekDay : weekDays) {
                    if (targetWeekDay > currentWeekDay) {
                        int daysToAdd = targetWeekDay - currentWeekDay;
                        foundInThisWeek = DateUtil.offsetDay(currentDate, daysToAdd);
                        break;
                    }
                }

                if (foundInThisWeek != null) {
                    firstMedicationDate = foundInThisWeek;
                } else {
                    // 本周没有找到，从下周的第一个选择星期几开始
                    int daysToNextWeek = 7 - currentWeekDay + weekDays.get(0);
                    firstMedicationDate = DateUtil.offsetDay(currentDate, daysToNextWeek);
                }
            }

            // 从第一个用药日期开始，按照选择的星期几生成所有用药日期
            Date currentMedicationWeek = firstMedicationDate;

            // 计算第一个用药日期所在周的周一
            int firstWeekDay = DateUtil.dayOfWeek(firstMedicationDate);
            if (firstWeekDay == 1) firstWeekDay = 7;
            else firstWeekDay = firstWeekDay - 1;

            Date weekMonday = DateUtil.offsetDay(firstMedicationDate, 1 - firstWeekDay);

            while (medicationDates.size() < totalDoses) {
                // 在当前周内添加所有选择的用药日期
                for (int targetWeekDay : weekDays) {
                    if (medicationDates.size() >= totalDoses) break;

                    // 计算具体日期（周一=0，周二=1，...，周日=6）
                    int dayOffset = targetWeekDay == 7 ? 6 : targetWeekDay - 1;
                    Date medicationDate = DateUtil.offsetDay(weekMonday, dayOffset);

                    // 只添加不早于第一个用药日期的日期
                    if (!medicationDate.before(firstMedicationDate)) {
                        medicationDates.add(medicationDate);
                    }
                }

                // 移动到下一周
                weekMonday = DateUtil.offsetDay(weekMonday, 7);
            }

            // 返回最后一次用药日期

            return medicationDates.isEmpty() ? null : medicationDates.get(medicationDates.size() - 1);

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析星期设置字符串
     *
     * @param weekDay       星期字符串（如"一，二，日"）
     * @param expectedCount 预期的星期数量
     * @return 星期数字列表（1=周一，7=周日）
     */
    private List<Integer> parseWeekDays(String weekDay, int expectedCount) {
        List<Integer> result = new ArrayList<>();

        if (weekDay == null || weekDay.trim().isEmpty()) {
            return result;
        }

        // 星期映射
        Map<String, Integer> weekDayMap = new HashMap<>();
        weekDayMap.put("一", 1);
        weekDayMap.put("二", 2);
        weekDayMap.put("三", 3);
        weekDayMap.put("四", 4);
        weekDayMap.put("五", 5);
        weekDayMap.put("六", 6);
        weekDayMap.put("日", 7);

        // 分割并解析
        String[] days = weekDay.split("[，,]");
        for (String day : days) {
            day = day.trim();
            if (weekDayMap.containsKey(day)) {
                result.add(weekDayMap.get(day));
            }
        }

        // 排序
        result.sort(Integer::compareTo);

        // 如果用户选择错误，只取前面的几个
        if (result.size() > expectedCount) {
            result = result.subList(0, expectedCount);
        }

        return result;
    }
}
