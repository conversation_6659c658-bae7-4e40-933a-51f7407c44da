/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80018 (8.0.18)
 Source Host           : ************:33068
 Source Schema         : yzlc

 Target Server Type    : MySQL
 Target Server Version : 80018 (8.0.18)
 File Encoding         : 65001

 Date: 05/08/2025 14:51:40
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for out_stop_medicine
-- ----------------------------
DROP TABLE IF EXISTS `out_stop_medicine`;
CREATE TABLE `out_stop_medicine`  (
  `id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID (雪花ID)',
  `medication_order_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '医嘱id',
  `area` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域',
  `bed_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '床位号',
  `hospitalization_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '住院号',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `status` int(11) NOT NULL COMMENT '状态1停药2回院',
  `drug_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品名称',
  `drug_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品规格',
  `single_dose` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '次用量',
  `order_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '医嘱名称',
  `daily_usage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '每日用量',
  `stopped_date` date NULL DEFAULT NULL COMMENT '停止日期',
  `recover_date` date NULL DEFAULT NULL COMMENT '恢复日期',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志 (0:未删除, 1:已删除)',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '记录最后修改人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '外出停药' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of out_stop_medicine
-- ----------------------------
INSERT INTO `out_stop_medicine` VALUES ('1949380136105218048', '1940702534112915500', '二号楼四楼', '2-406-24', '800352', '王升凤', 1, '云南白药气雾剂', '85g+60g', '早1片', '用药医嘱', NULL, '2025-07-27', NULL, '', 1, 'admin', '2025-07-27 16:03:37', 'admin', '2025-07-27 16:09:42');
INSERT INTO `out_stop_medicine` VALUES ('1949380289583190016', '1940732378745966600', '二号楼四楼', '2-406-24', '800352', '王升凤', 1, '盐酸小檗碱片', '0.1g*100片', '早1片，中2片', '用药医嘱', NULL, '2025-07-27', NULL, '', 1, 'admin', '2025-07-27 16:04:14', 'admin', '2025-07-27 16:09:38');
INSERT INTO `out_stop_medicine` VALUES ('1949381754808766464', '1940702534112915500', '二号楼四楼', '2-406-24', '800352', '王升凤', 2, '云南白药气雾剂', '85g+60g', '早1片', '用药医嘱', 'Qd', '2025-07-27', '2025-07-27', '666', 0, 'admin', '2025-07-27 16:10:03', 'admin', '2025-07-27 16:56:17');
INSERT INTO `out_stop_medicine` VALUES ('1949393908769284096', '1949393735490003000', '二号楼二楼', '2-218-58', '800724', '张杏林', 2, '盐酸小檗碱片', '0.1g*100片', '早50片', '用药医嘱', 'Qd', '2025-07-26', '2025-07-27', '', 1, 'admin', '2025-07-27 16:58:21', 'admin', '2025-07-27 16:59:37');
INSERT INTO `out_stop_medicine` VALUES ('1949394253050339328', '1949393735490003000', '二号楼二楼', '2-218-58', '800724', '张杏林', 2, '盐酸小檗碱片', '0.1g*100片', '早20片', '用药医嘱', 'Qd', '2025-07-25', '2025-07-27', '', 1, 'admin', '2025-07-27 16:59:43', 'admin', '2025-07-27 17:01:17');

SET FOREIGN_KEY_CHECKS = 1;
