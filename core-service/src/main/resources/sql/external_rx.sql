/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80018 (8.0.18)
 Source Host           : ************:33068
 Source Schema         : yzlc

 Target Server Type    : MySQL
 Target Server Version : 80018 (8.0.18)
 File Encoding         : 65001

 Date: 31/07/2025 15:58:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for external_rx
-- ----------------------------
DROP TABLE IF EXISTS `external_rx`;
CREATE TABLE `external_rx`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID（雪花ID）',
  `medical_record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门诊病历id',
  `prescription_number` bigint(20) NULL DEFAULT NULL COMMENT '处方号',
  `prescription_date` datetime NULL DEFAULT NULL COMMENT '处方日期',
  `drug_stock_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '外配药库存id',
  `drug_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品名称',
  `drug_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品规格',
  `drug_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品单位',
  `status` int(11) NOT NULL COMMENT '状态',
  `part` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用药部位',
  `administration_route` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '给药途径',
  `administration_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '给药方法',
  `prescription_quantity` int(11) NULL DEFAULT NULL COMMENT '药品数量',
  `week_day` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '星期',
  `morning_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '早（数量）',
  `morning_time` time NULL DEFAULT NULL COMMENT '早（时间）',
  `noon_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '中（数量）',
  `noon_time` time NULL DEFAULT NULL COMMENT '中（时间）',
  `evening_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '晚（数量）',
  `evening_time` time NULL DEFAULT NULL COMMENT '晚（时间）',
  `night_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '夜（数量）',
  `night_time` time NULL DEFAULT NULL COMMENT '夜（时间）',
  `prompt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '提示',
  `drug_source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '药品来源',
  `single_dose` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '次用量',
  `single_dose_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '次用量单位',
  `order_date` date NULL DEFAULT NULL COMMENT '医嘱日期',
  `attending_doctor_id` bigint(20) NULL DEFAULT NULL COMMENT '带教医生ID',
  `order_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '医嘱类型',
  `order_time` time NULL DEFAULT NULL COMMENT '医嘱时间',
  `disease_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '长期医嘱的用药类型',
  `doctor_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '医生姓名',
  `nurse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '护士姓名',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除字段（0:未删除 1:已删除）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '外配药医嘱表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of external_rx
-- ----------------------------
INSERT INTO `external_rx` VALUES (1950827475764359168, '1949393563313823744', 806869, '2025-07-31 15:54:51', '1949687506515963904', '阿司匹林肠溶片', '0.1g*30片/盒/盒,铝塑包装', '盒', 1, '', '口服', 'Qd', NULL, '', NULL, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', '家属', '0.1', 'g', '2025-07-27', NULL, '长期', '16:56:39', '慢性病用药', '', '', 0, '2025-07-31 15:54:51', 'admin', '2025-07-31 15:54:52', 'admin');

SET FOREIGN_KEY_CHECKS = 1;
